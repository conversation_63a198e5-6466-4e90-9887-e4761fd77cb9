package model

import (
	"reflect"
	"testing"
)

func TestAssetFieldMap(t *testing.T) {
	// 测试 assstFieldMap 是否正确初始化
	if assstFieldMap == nil {
		t.Fatal("assstFieldMap should be initialized")
	}

	// 获取 Asset 结构体的所有字段
	assetType := reflect.TypeOf(Asset{})
	expectedFields := make(map[string]bool)

	for i := 0; i < assetType.NumField(); i++ {
		field := assetType.Field(i)
		tag := field.Tag.Get("vmedia")
		if tag != "" {
			expectedFields[tag] = true
		}
	}

	// 验证 assstFieldMap 包含所有预期的字段
	for field := range expectedFields {
		if !assstFieldMap[field] {
			t.<PERSON>("Expected field '%s' not found in assstFieldMap", field)
		}
	}

	// 验证 assstFieldMap 不包含意外的字段
	for field := range assstFieldMap {
		if !expectedFields[field] {
			t.<PERSON>("Unexpected field '%s' found in assstFieldMap", field)
		}
	}

	// 打印字段映射内容以便调试
	t.Logf("assstFieldMap contents: %v", assstFieldMap)
	t.Logf("Expected fields: %v", expectedFields)
}

func TestIsValidAssetField(t *testing.T) {
	// 测试有效字段
	validFields := []string{
		"primary_key",
		"tags",
		"asset_type",
		"data",
		"state",
		"belveth_fid",
		"project_id_list",
	}

	for _, field := range validFields {
		if !IsValidAssetField(field) {
			t.Errorf("Field '%s' should be valid", field)
		}
	}

	// 测试无效字段
	invalidFields := []string{
		"invalid_field",
		"",
		"id",
		"assettype",
	}

	for _, field := range invalidFields {
		if IsValidAssetField(field) {
			t.Errorf("Field '%s' should be invalid", field)
		}
	}
}

func TestAssetValid(t *testing.T) {
	// 测试有效的资产
	validAsset := &Asset{
		ID:        "test-id",
		AssetType: Other,
		State:     AssetStateValid,
	}

	if !validAsset.Valid() {
		t.Error("Asset should be valid")
	}

	// 测试无效的资产 - 缺少 ID
	invalidAsset1 := &Asset{
		AssetType: Other,
		State:     AssetStateValid,
	}

	if invalidAsset1.Valid() {
		t.Error("Asset without ID should be invalid")
	}

	// 测试无效的资产 - 缺少 AssetType
	invalidAsset2 := &Asset{
		ID:    "test-id",
		State: AssetStateValid,
	}

	if invalidAsset2.Valid() {
		t.Error("Asset without AssetType should be invalid")
	}

	// 测试无效的资产 - 状态不是有效状态
	invalidAsset3 := &Asset{
		ID:        "test-id",
		AssetType: Other,
		State:     AssetStateDeleted,
	}

	if invalidAsset3.Valid() {
		t.Error("Asset with deleted state should be invalid")
	}
}

func TestSearchCondToUniversalSearchCond(t *testing.T) {
	// 测试 EQ 操作符
	eqCond := &SearchCond{
		Field:  "asset_type",
		Op:     "EQ",
		Values: "Map",
	}

	expected := "asset_type=Map"
	if result := eqCond.ToUniversalSearchCond(); result != expected {
		t.Errorf("Expected '%s', got '%s'", expected, result)
	}

	// 测试非 EQ 操作符
	otherCond := &SearchCond{
		Field:  "tags",
		Op:     "IN",
		Values: "tag1,tag2",
	}

	expected = "tags=[IN]tag1,tag2"
	if result := otherCond.ToUniversalSearchCond(); result != expected {
		t.Errorf("Expected '%s', got '%s'", expected, result)
	}
}
