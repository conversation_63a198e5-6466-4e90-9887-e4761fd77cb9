package model

import (
	"context"
	"time"
)

const (
	AssetUploadJobStateInit    = 0
	AssetUploadJobStateSuccess = 1
	AssetUploadJobStateFailed  = 2
)

// AssetUploadJob 资产上传任务
type AssetUploadJob struct {
	ID        int64     `json:"id" gorm:"primaryKey;autoIncrement"` // 物理主键，在逻辑里不使用这个字段
	JobID     string    `json:"job_id" gorm:"not null"`             // 这是belveth返回的jobID
	AssetID   string    `json:"asset_id" gorm:"not null"`
	FileName  string    `json:"file_name" gorm:"not null"`
	Fid       string    `json:"fid" gorm:"not null"`
	State     int       `json:"state" gorm:"not null"`
	Reason    string    `json:"reason" gorm:"not null"`
	CreatedBy string    `json:"created_by" gorm:"	:created_by"`
	UpdatedBy string    `json:"updated_by" gorm:"column:updated_by"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at;default:CURRENT_TIMESTAMP;autoUpdateTime"`
}

// TableName 表名
func (a *AssetUploadJob) TableName() string {
	return "t_asset_upload_job"
}

func (a *AssetUploadJob) SetSuccess(fid string) {
	a.Fid = fid
	a.State = AssetUploadJobStateSuccess
}

func (a *AssetUploadJob) SetFailed(reason string) {
	a.State = AssetUploadJobStateFailed
	a.Reason = reason
}

// AssetUploadJobRepo 资产上传任务仓库
type AssetUploadJobRepo interface {
	// Create 创建任务
	Create(ctx context.Context, assetID, jobID, operator, fileName string) (int64, error)
	// GetOngoingJob 获取正在进行的任务
	GetOngoingJob(ctx context.Context, assetID string) (*AssetUploadJob, error)
	// Save 保存任务
	Save(ctx context.Context, job *AssetUploadJob) error
}
