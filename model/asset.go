package model

import (
	"context"
	"fmt"
	"reflect"
	"slices"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/BSC/previz_backend/cms_admin/consts"
	gate "git.woa.com/trpcprotocol/video_media/belveth_gateway_belveth"
)

const (
	Character = "1" // 角色模型
	Scene     = "2" // 场景模型
	Prop      = "3" // 道具模型
	Audio     = "4" // 音频文件
	Image     = "5" // 图片文件
	Other     = "6" // 其他

	AssetStateValid   = 1
	AssetStateDeleted = 2
)

// Asset 资产
type Asset struct {
	ID              string   `vmedia:"primary_key"`
	Name            string   `vmedia:"name"`
	Tags            []string `vmedia:"tags"`
	AssetType       string   `vmedia:"asset_type"`
	Data            string   `vmedia:"data"`
	State           int      `vmedia:"state"`
	BelvethFid      string   `vmedia:"belveth_fid"`
	BelvethFilename string   `vmedia:"belveth_filename"`
	ProjectIdList   []string `vmedia:"project_id_list"`
	ThumbnailFid    string   `vmedia:"thumbnail_fid"`
	CreatedBy       string   `vmedia:"created_by"`
	CreatedAt       int64    `vmedia:"created_at"`
}

var (
	assstFieldMap     map[string]bool
	assstFieldMapOnce sync.Once
	validAssetTypes   = []string{Character, Scene, Prop, Audio, Image, Other}
)

func init() {
	assstFieldMapOnce.Do(func() {
		assstFieldMap = make(map[string]bool)
		assetType := reflect.TypeOf(Asset{})
		for i := 0; i < assetType.NumField(); i++ {
			field := assetType.Field(i)
			tag := field.Tag.Get("vmedia")
			if tag != "" {
				assstFieldMap[tag] = true
			}
		}
	})
}

// IsValidAssetField 判断字段是否有效
func IsValidAssetField(field string) bool {
	return assstFieldMap[field]
}

// ValidateFields 验证资产更新字段合法
func (a *Asset) ValidateFields() error {
	if at := a.AssetType; at != "" && !slices.Contains(validAssetTypes, at) {
		return errs.Newf(consts.CodeInvalidParam, "资产类型不合法")
	}
	if s := a.State; s > 0 && s != AssetStateValid {
		return errs.Newf(consts.CodeInvalidParam, "资产状态不合法")
	}
	return nil
}

// Valid 判断资产是否有效
func (a *Asset) Valid() bool {
	return a.ID != "" && a.AssetType != "" && a.State == AssetStateValid
}

// AssetRepo 资产仓库
type AssetRepo interface {
	// GetAsset 获取资产
	GetAsset(ctx context.Context, id string) (Asset, error)
	// CreateAsset 创建资产
	CreateAsset(ctx context.Context, asset Asset, user *gate.User) (string, error)
	// UpdateAsset 更新资产
	UpdateAsset(ctx context.Context, asset Asset, user *gate.User) error
	// DeleteAsset 删除资产
	DeleteAsset(ctx context.Context, id string, user *gate.User) error
	// SearchAssets 查询资产
	SearchAssets(ctx context.Context, req *SearchAssetsReq) (Pagination, []Asset, error)
	// AddAssetProject 添加资产和项目关联关系
	AddAssetProject(ctx context.Context, assetID, projectID string, user *gate.User) error
	// RemoveAssetProject 删除资产和项目关联关系
	RemoveAssetProject(ctx context.Context, assetID, projectID string, user *gate.User) error
}

// SearchAssetsReq 搜索资产请求
type SearchAssetsReq struct {
	Page             int32
	PageSize         int32
	SearchCondGroups []SearchCondGroup
	Sort             []SortInfo
}

// SearchCondGroup 搜索条件组
type SearchCondGroup struct {
	Conds []SearchCond
}

// SearchCond 搜索条件
type SearchCond struct {
	Field  string
	Op     string
	Values string
}

// ToUniversalSearchCond 转换为Universal搜索条件
func (s *SearchCond) ToUniversalSearchCond() string {
	if s.Op == "EQ" {
		return fmt.Sprintf("%s=%s", s.Field, s.Values)
	}
	return fmt.Sprintf("%s=[%s]%s", s.Field, s.Op, s.Values)
}

// SortInfo 排序信息
type SortInfo struct {
	Field string
	Order string
}

// Pagination 分页信息
type Pagination struct {
	Page     int32
	PageSize int32
	Total    int32
}
