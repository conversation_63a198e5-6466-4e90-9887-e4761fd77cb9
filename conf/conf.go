// Package conf provides configuration management.
package conf

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/BSC/previz_backend/cms_admin/util"
	"gopkg.in/yaml.v2"
)

const (
	configFile = "cms_admin.yaml"
)

// Config represents the configuration for the distributor.
type Config struct {
	Namespace  string           `yaml:"namespace"`
	Universal  UniversalConfig  `yaml:"universal"`
	JobManager JobManagerConfig `yaml:"job_manager"`
}

// UniversalConfig 统一接入平台配置
type UniversalConfig struct {
	Timeout         int        `yaml:"timeout"`
	Namespace       string     `yaml:"namespace"`
	ReadAuth        AuthConfig `yaml:"read_auth"`
	WriteAuth       AuthConfig `yaml:"write_auth"`
	CreateAuth      AuthConfig `yaml:"create_auth"`
	SearchAuth      AuthConfig `yaml:"search_auth"`
	ReadBatchSize   int        `yaml:"read_batch_size"`
	SearchBatchSize int        `yaml:"search_batch_size"`
}

// JobManagerConfig 任务管理器配置
type JobManagerConfig struct {
	Namespace string `yaml:"namespace"`
}

type AuthConfig struct {
	AppID  string `yaml:"app_id"`
	AppKey string `yaml:"app_key"`
}

var gCfg *Config

// GetConfig returns the global configuration.
func GetConfig() *Config {
	return gCfg
}

// InitConfig 初始化全局配置
func InitConfig(ctx context.Context) error {
	gCfg = &Config{}
	// 读取配置并反序列化到yf中
	if err := config.GetYAML(configFile, gCfg); err != nil {
		return fmt.Errorf("init global config failed, err: %v", err)
	}
	watch, err := config.Get("rainbow1").Watch(ctx, configFile)
	if err != nil {
		return fmt.Errorf("init global config set watch error, err: %v", err)
	}
	go func() {
		for r := range watch {
			newCfg := &Config{}
			if err := yaml.Unmarshal([]byte(r.Value()), newCfg); err != nil {
				log.Warnf("watch global config, unmarshal error, err: %v", err)
			} else {
				log.Infof("watch global config, updated, conf: %v", util.Marshal(newCfg))
				gCfg = newCfg
			}
		}
	}()
	return nil
}
