// Package util provides some common util functions.
package util

import "encoding/json"

// Marshal 将任意类型转换为json字符串
func Marshal(v any) string {
	bs, err := json.Marshal(v)
	if err != nil {
		return ""
	}
	return string(bs)
}

// IsJSON 判断字符串是否为json
func IsJSON(s string) bool {
	var js json.RawMessage
	return json.Unmarshal([]byte(s), &js) == nil
}

// RemoveEmptyString 移除空字符串
func RemoveEmptyString(slice []string) []string {
	var result []string
	for _, s := range slice {
		if s != "" {
			result = append(result, s)
		}
	}
	return result
}
