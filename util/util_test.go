package util

import (
	"testing"
)

func TestMarshal(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected string
	}{
		{
			name:     "simple string",
			input:    "hello",
			expected: `"hello"`,
		},
		{
			name:     "simple number",
			input:    123,
			expected: "123",
		},
		{
			name: "struct",
			input: struct {
				Name string `json:"name"`
				Age  int    `json:"age"`
			}{
				Name: "test",
				Age:  25,
			},
			expected: `{"name":"test","age":25}`,
		},
		{
			name:     "slice",
			input:    []string{"a", "b", "c"},
			expected: `["a","b","c"]`,
		},
		{
			name:     "nil",
			input:    nil,
			expected: "null",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Marshal(tt.input)
			if result != tt.expected {
				t.<PERSON><PERSON><PERSON>("Marshal() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestMarshalInvalidInput(t *testing.T) {
	// Test with channel which cannot be marshaled
	ch := make(chan int)
	result := Marshal(ch)
	if result != "" {
		t.<PERSON><PERSON><PERSON>("Marshal() with invalid input should return empty string, got %v", result)
	}
}

func TestIsJSON(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "valid json object",
			input:    `{"name":"test","age":25}`,
			expected: true,
		},
		{
			name:     "valid json array",
			input:    `["a","b","c"]`,
			expected: true,
		},
		{
			name:     "valid json string",
			input:    `"hello"`,
			expected: true,
		},
		{
			name:     "valid json number",
			input:    `123`,
			expected: true,
		},
		{
			name:     "valid json boolean",
			input:    `true`,
			expected: true,
		},
		{
			name:     "valid json null",
			input:    `null`,
			expected: true,
		},
		{
			name:     "invalid json - incomplete object",
			input:    `{"name":"test"`,
			expected: false,
		},
		{
			name:     "invalid json - wrong syntax",
			input:    `{name:"test"}`,
			expected: false,
		},
		{
			name:     "empty string",
			input:    "",
			expected: false,
		},
		{
			name:     "plain text",
			input:    "hello world",
			expected: false,
		},
		{
			name:     "token string",
			input:    "abc123def",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsJSON(tt.input)
			if result != tt.expected {
				t.Errorf("IsJSON() = %v, want %v", result, tt.expected)
			}
		})
	}
}
