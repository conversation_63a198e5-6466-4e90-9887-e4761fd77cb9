# .code.yml规范:
# https://iwiki.woa.com/pages/viewpage.action?pageId=289510758

# VEPC相关配置
vepc:
  # VEPC基础配置
  basic:
    name: "123://video_media.cms_admin/go/trpc"

  # ci 参数
  # .code.yml 控制流水线参数 https://iwiki.woa.com/pages/viewpage.action?pageId=403997263
  ci:
    params:
      vepc_nerver_use_key: "1"  # 占位保证节点不为空。提示层级防止开发同学写错
      # ng_test_blacklist_file: "test/.*,stub/.*"  # 屏蔽测试覆盖率


# 定义项目分支命名规范,可以根据业务实际分支类型，填写相应的命名规范，对不适合的分支项可以删除
# 所有分支命名推荐统一使用小写字母
branch:
  # 主干分支
  trunk_name: "master"
  branch_type_A:
    personal_feature:
      pattern: "feature/.*"
    bugfix:
      pattern: "bugfix/.*"
    tag:
      pattern: "versionnumber_yyyyMMddHHmm"
      # versionnumber特指版本号
      versionnumber: "Major.Feature.Fix.BuildNo"

artifact:
  # 大仓的情况，可能不同目录是不同的发布单元,下面path指定代码仓库里相应发布单元目录
  # 若整个仓库代码是一个发布单元，该path为当前目录
  - path: "/"
    # 发布单元名称，如AndroidQQ、epc-demo
    artifact_name: "cms_admin"
    #发布单元类型，可选类型字段有，移动端/PC端/后台单体应用/后台微服务/web/sdk/lib/框架
    #比如发布单元为腾讯视频Android客户端，则发布单元类型为如下样例 移动端
    artifact_type: "后台微服务"

    # 提供产品发布单元发布制品归档地址
    # 地址定义说明详见：https://git.code.oa.com/epcm/new_epc_tmp/blob/master/0F.artifacts-release-spec.md
    repository_url: "https://mirrors.tencent.com/repository/generic/qqlive/packages/video_media.cms_admin/"

# 屏蔽 codecc 代码检查 https://iwiki.woa.com/pages/viewpage.action?pageId=162054609
source:
  # 提供产品代码库中编写的测试代码存放目录或文件名格式,以便后续代码统计环节进行排除等特殊处理
  test_source:
    # 提供相对路径格式的测试代码目录，路径支持通配符方式描述
    # directory_path: ["./cms_admin/client/"]
    # 若以上指定的测试代码目录都是项目测试代码，测试代码文件命名标识可以为空
    # 测试代码文件的正则表达式
    filepath_regex: [".*_test.*", "./cms_admin/client/.*", "./test/.*"]

  # 提供产品代码库中工具或框架自动生成的且在代码库中的代码，没有可为空。以便后续代码统计环节进行排除等特殊处理。
  auto_generate_source:
    # 自动生成代码文件的正则表达式，若无统一标识格式，可以指定具体目录，样例可参考test_source举例
    filepath_regex: [".*stub/.*"]

  # 提供产品代码库中直接以源码形式存在的第三方代码目录或代码文件名的正则表达。
  # 此处备注的第三方代码在后续统计代码量环节会被排除，若代码库中不存在需要排除的第三方代码，该项配置标识可为空
  third_party_source:
    #第三方代码文件的正则表达式，若无统一标识格式，可以指定具体目录，样例可参考test_source举例
    filepath_regex: [".*vendor/.*", ".*third_party/.*"]

# 针对整个项目默认代码的评审配置，不包括保护分支
code_review:
  # 定义产品工蜂cr的评论标签
  restrict_labels: ["CR-编程规范", "CR-业务逻辑","CR-边界逻辑","CR-代码架构","CR-性能影响","CR-安全性","CR-可测试性","CR-可读性"]
  # 评审人
  reviewers: ["juncjychen","cyancui"]
  # 必要评审人
  necessary_reviewers: ["juncjychen","cyancui"]

# 自定义文件或目录owner和代码评审配置
# 文件或目录可使用绝对或相对路径，绝对路径按代码库根目录计算，以/开头。相对路径按.code.yml所在目录来判断，以 ./开头
file:
  - path: ".*"
    owners: ["juncjychen","cyancui"]
    # owner_rule, 必填项，文件负责人通过规则可选值 -1,0,大于等于1的整数;
    # -1,表示需所有owner审批；
    # 0，表示该文件无需任意一个owner审批;
    # 用大于等于1的整数，表示需要相应整数个的owner审批该路径，比如2，标识需要任意两个owners审批
    owner_rule: 1
    # 以下code_review为可选项，该项表示路径评审，但评审是针对整个CR单，非owner机制中只评审单个文件
    code_review:
      reviewers:  ["juncjychen","cyancui"]
      necessary_reviewers:  ["juncjychen","cyancui"]

# .code.yml版本，后续可能会自动升级到新版本
version: 1.2
