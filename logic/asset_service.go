package logic

import (
	"context"
	"strconv"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/BSC/previz_backend/cms_admin/consts"
	"git.woa.com/BSC/previz_backend/cms_admin/model"
	"git.woa.com/BSC/previz_backend/cms_admin/util"
	pb "git.woa.com/trpcprotocol/video_media/cms_admin_admin"
	"github.com/korovkin/limiter"
)

var (
	noopAsset  = model.Asset{}
	successRsp = &pb.CommonRsp{
		Code: 0,
		Msg:  "success",
	}
)

// AssetService 资产服务
type AssetService struct {
	*provider
}

// NewAssetService 创建资产服务
func NewAssetService() *AssetService {
	return &AssetService{
		provider: getProvider(),
	}
}

// ListAssets 查询资产
func (a *AssetService) SearchAssets(ctx context.Context, req *pb.SearchAssetsRequest) (*pb.SearchAssetsResponse, error) {
	log.Infof("search assets, req: %v", util.Marshal(req))
	repo := a.assetRepo
	searchReq := &model.SearchAssetsReq{
		Page:             req.Page,
		PageSize:         req.PageSize,
		SearchCondGroups: toSearchCondGroups(req.SearchConditionGroups),
		Sort:             toSortInfos(req.Sort),
	}
	if err := a.validateSearchReq(req); err != nil {
		return nil, err
	}
	appendStateValidConds(searchReq)
	pagination, assets, err := repo.SearchAssets(ctx, searchReq)
	if err != nil {
		return nil, err
	}
	return &pb.SearchAssetsResponse{
		Total:  pagination.Total,
		Assets: toPbAssets(assets),
	}, nil
}

// appendStateValidConds 添加条件，避免返回已删除的资产
func appendStateValidConds(searchReq *model.SearchAssetsReq) {
	for i := range searchReq.SearchCondGroups {
		searchReq.SearchCondGroups[i].Conds = append(
			searchReq.SearchCondGroups[i].Conds, model.SearchCond{
				Field:  "state",
				Op:     "EQ",
				Values: strconv.Itoa(model.AssetStateValid),
			})
	}
}

func (a *AssetService) validateSearchReq(req *pb.SearchAssetsRequest) error {
	for _, condGroup := range req.SearchConditionGroups {
		for _, cond := range condGroup.Conds {
			if !model.IsValidAssetField(cond.Field) {
				return errs.Newf(consts.CodeInvalidParam, "field must be valid")
			}
		}
	}
	if len(req.Sort) > 0 {
		for _, sort := range req.Sort {
			if !model.IsValidAssetField(sort.Field) {
				return errs.Newf(consts.CodeInvalidParam, "field must be valid")
			}
		}
	}
	return nil
}

// CreateAsset 创建资产
func (a *AssetService) CreateAsset(ctx context.Context, req *pb.CreateAssetRequest) (*pb.CreateAssetResponse, error) {
	user, err := currentUser(ctx)
	if err != nil {
		return nil, err
	}
	log.Infof("create asset, req: %v, user: %v", util.Marshal(req), util.Marshal(user))
	repo := a.assetRepo
	asset := model.Asset{
		Tags:         req.Tags,
		AssetType:    req.AssetType,
		Data:         req.Data,
		Name:         req.Name,
		ThumbnailFid: req.ThumbnailFid,
		State:        model.AssetStateValid,
		CreatedBy:    userRTXorBvID(user),
		CreatedAt:    time.Now().Unix(),
	}
	if err := asset.ValidateFields(); err != nil {
		return nil, err
	}

	assetID, err := repo.CreateAsset(ctx, asset, user)
	if err != nil {
		return nil, err
	}
	return &pb.CreateAssetResponse{
		AssetId: assetID,
	}, nil
}

// UpdateAsset 更新资产
func (a *AssetService) UpdateAsset(ctx context.Context, req *pb.UpdateAssetRequest) (*pb.UpdateAssetResponse, error) {
	asset := model.Asset{
		ID:           req.AssetId,
		Tags:         req.Tags,
		Data:         req.Data,
		AssetType:    req.AssetType,
		Name:         req.Name,
		ThumbnailFid: req.ThumbnailFid,
	}
	if err := asset.ValidateFields(); err != nil {
		return nil, err
	}
	user, err := currentUser(ctx)
	if err != nil {
		return nil, err
	}
	if err := a.assetRepo.UpdateAsset(ctx, asset, user); err != nil {
		return nil, err
	}
	return &pb.UpdateAssetResponse{
		Rsp: &pb.CommonRsp{
			Code: 0,
			Msg:  "success",
		},
	}, nil
}

// DeleteAsset 删除资产
func (a *AssetService) DeleteAsset(ctx context.Context, req *pb.DeleteAssetRequest) (*pb.DeleteAssetResponse, error) {
	user, err := currentUser(ctx)
	if err != nil {
		return nil, err
	}
	if err := a.assetRepo.DeleteAsset(ctx, req.AssetId, user); err != nil {
		return nil, err
	}
	return &pb.DeleteAssetResponse{Rsp: successRsp}, nil
}

// GetAsset 获取资产
func (a *AssetService) GetAsset(ctx context.Context, req *pb.GetAssetRequest) (*pb.GetAssetResponse, error) {
	user, err := currentUser(ctx)
	if err != nil {
		return nil, err
	}
	log.Infof("user: %v", util.Marshal(user))
	asset, err := a.assetRepo.GetAsset(ctx, req.AssetId)
	if err != nil {
		return nil, err
	}
	var ret *pb.Asset
	if asset.Valid() {
		ret = toPbAsset(asset)
	}
	return &pb.GetAssetResponse{
		Rsp:   successRsp,
		Asset: ret,
	}, nil
}

// ./trpc-cli -func /trpc.video_media.cms_admin.AssetService/StartUpload -body '{"asset_id": "2dei93s1vw", "file_name": "testmp4"}'
// StartUpload 开始上传
func (a *AssetService) StartUpload(ctx context.Context, req *pb.StartUploadRequest) (*pb.StartUploadResponse, error) {
	assetID, fileName, contentType := req.AssetId, req.FileName, req.ContentType
	if assetID == "" || fileName == "" {
		return nil, errs.Newf(consts.CodeInvalidParam, "asset_id and file_name are required")
	}
	user, err := currentUser(ctx)
	if err != nil {
		return nil, err
	}
	if _, err := a.ensureAssetValid(ctx, assetID); err != nil {
		return nil, err
	}
	if job, err := a.assetUploadJobRepo.GetOngoingJob(ctx, assetID); err != nil {
		return nil, err
	} else if job != nil {
		log.Errorf("asset is uploading, assetID: %s, jobID: %s", assetID, job.ID)
		return nil, errs.Newf(consts.CodeAssetIsUploading, "asset is uploading")
	}
	jobID, err := a.assetUploader.Start(ctx, assetID, fileName, contentType, user.GetId())
	if err != nil {
		log.Errorf("start upload failed, assetID: %s, err: %v", assetID, err)
		return nil, errs.Newf(consts.CodeInternal, "start upload failed, err: %v", err)
	}
	if cnt, err := a.assetUploadJobRepo.Create(ctx, assetID, jobID, user.GetDetail().GetBvDetail().GetRtx(), fileName); err != nil {
		log.Errorf("save job failed, assetID: %s, err: %v", assetID, err)
		return nil, errs.Newf(consts.CodeInternal, "save job failed, err: %v", err)
	} else if cnt == 0 {
		log.Errorf("save job failed, assetID: %s, jobID: %s", assetID, jobID)
		return nil, errs.Newf(consts.CodeInternal, "save job failed, jobID: %s", jobID)
	}
	return &pb.StartUploadResponse{
		Rsp: successRsp,
		UploadJob: &pb.AssetUploadJob{
			Id: jobID,
		},
	}, nil
}

func (a *AssetService) ensureAssetValid(ctx context.Context, assetID string) (model.Asset, error) {
	asset, err := a.assetRepo.GetAsset(ctx, assetID)
	if err != nil {
		return noopAsset, errs.Newf(consts.CodeInternal, "get asset failed, err: %v", err)
	}
	if !asset.Valid() {
		return noopAsset, errs.Newf(consts.CodeInternal, "asset not valid, assetID: %s", assetID)
	}
	return asset, nil
}

// FinishUpload 完成上传
func (a *AssetService) FinishUpload(ctx context.Context, req *pb.FinishUploadRequest) (*pb.FinishUploadResponse, error) {
	jobID := req.JobId
	assetID := req.AssetId
	isSuccess := req.Success
	failReason := req.FailReason
	total := req.Total
	user, err := currentUser(ctx)
	if err != nil {
		return nil, err
	}
	if total <= 0 {
		return nil, errs.Newf(consts.CodeInvalidParam, "total is required")
	}
	job, err := a.assetUploadJobRepo.GetOngoingJob(ctx, assetID)
	if err != nil {
		return nil, err
	}
	if job == nil || job.JobID != jobID {
		return nil, errs.Newf(consts.CodeInternal, "job not found")
	}
	if !isSuccess {
		job.SetFailed(failReason)
		if err := a.assetUploadJobRepo.Save(ctx, job); err != nil {
			return nil, err
		}
		return &pb.FinishUploadResponse{
			Rsp: successRsp,
		}, nil
	}
	asset, err := a.ensureAssetValid(ctx, assetID)
	if err != nil {
		return nil, err
	}
	fid, err := a.assetUploader.Complete(ctx, jobID, int(total))

	if err != nil {
		log.Errorf("complete upload failed, assetID: %s, jobID: %s, operator: %+v, err: %v",
			assetID, jobID, user.GetDetail().GetBvDetail(), err)
		return nil, err
	}
	log.Infof("complete upload, assetID: %s, jobID: %s, operator: %+v, fid: %s",
		assetID, jobID, user.GetDetail().GetBvDetail(), fid)
	job.SetSuccess(fid)
	if err := a.assetUploadJobRepo.Save(ctx, job); err != nil {
		return nil, err
	}
	asset.BelvethFid = fid
	asset.BelvethFilename = job.FileName
	if err := a.assetRepo.UpdateAsset(ctx, asset, user); err != nil {
		return nil, err
	}
	return &pb.FinishUploadResponse{
		Rsp: successRsp,
	}, nil
}

// SyncAssetProjectList 同步资产和项目关联关系
func (a *AssetService) SyncAssetProjectList(ctx context.Context, req *pb.SyncAssetProjectListRequest) (*pb.SyncAssetProjectListResponse, error) {
	projectID := req.ProjectId
	oriAssetIDs := util.RemoveEmptyString(req.OriAssetIds)
	newAssetIDs := util.RemoveEmptyString(req.NewAssetIds)

	// 参数验证
	if projectID == "" {
		return nil, errs.Newf(consts.CodeInvalidParam, "project_id is required")
	}
	user, err := currentUser(ctx)
	if err != nil {
		return nil, err
	}

	// 将切片转换为map，便于集合运算
	oriAssetMap := make(map[string]bool)
	for _, id := range oriAssetIDs {
		oriAssetMap[id] = true
	}

	newAssetMap := make(map[string]bool)
	for _, id := range newAssetIDs {
		newAssetMap[id] = true
	}

	// 计算需要新增关联的资产ID (new - ori)
	var toAdd []string
	for _, id := range newAssetIDs {
		if !oriAssetMap[id] {
			toAdd = append(toAdd, id)
		}
	}

	// 计算需要移除关联的资产ID (ori - new)
	var toRemove []string
	for _, id := range oriAssetIDs {
		if !newAssetMap[id] {
			toRemove = append(toRemove, id)
		}
	}
	ltr := limiter.NewConcurrencyLimiter(len(toAdd) + len(toRemove))
	// 并发执行添加和移除操作
	var mu sync.Mutex
	var fails []*pb.UpdateFailItem

	// 并发添加关联
	for _, item := range toAdd {
		assetID := item
		ltr.Execute(func() {
			if err := a.assetRepo.AddAssetProject(ctx, assetID, projectID, user); err != nil {
				mu.Lock()
				fails = append(fails, &pb.UpdateFailItem{
					AssetId: assetID,
					ErrCode: consts.CodeInternal,
					Reason:  err.Error(),
				})
				mu.Unlock()
			}
		})
	}

	// 并发移除关联
	for _, item := range toRemove {
		assetID := item
		ltr.Execute(func() {
			if err := a.assetRepo.RemoveAssetProject(ctx, assetID, projectID, user); err != nil {
				mu.Lock()
				fails = append(fails, &pb.UpdateFailItem{
					AssetId: assetID,
					ErrCode: consts.CodeInternal,
					Reason:  err.Error(),
				})
				mu.Unlock()
			}
		})
	}

	// 等待所有操作完成
	ltr.WaitAndClose()

	if len(fails) > 0 {
		return &pb.SyncAssetProjectListResponse{
			Rsp: &pb.CommonRsp{
				Code: consts.CodeSyncAssetProjectListFailed,
				Msg:  "sync asset project list partially failed",
			},
			Fails: fails,
		}, nil
	}
	// 构建响应
	return &pb.SyncAssetProjectListResponse{
		Rsp: &pb.CommonRsp{
			Code: 0,
			Msg:  "success",
		},
		Fails: fails,
	}, nil
}
