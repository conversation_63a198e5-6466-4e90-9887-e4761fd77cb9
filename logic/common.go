package logic

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/BSC/previz_backend/cms_admin/consts"
	"git.woa.com/BSC/previz_backend/cms_admin/model"
	gate "git.woa.com/trpcprotocol/video_media/belveth_gateway_belveth"
	pb "git.woa.com/trpcprotocol/video_media/cms_admin_admin"
)

// currentUser 获取当前用户
func currentUser(ctx context.Context) (*gate.User, error) {
	val := trpc.GetMetaData(ctx, gate.MetaKey_USER.String())
	var user gate.User
	if err := codec.Unmarshal(codec.SerializationTypePB, val, &user); err != nil {
		log.Errorf("unmarshal user failed, err: %v", err)
		return nil, err
	}
	if !userValid(&user) {
		return nil, errs.Newf(consts.CodeInternal, "not login")
	}
	return &user, nil
}

// userRTXorBvID 获取用户RTX或BvID
func userRTXorBvID(user *gate.User) string {
	if user == nil {
		return ""
	}
	if user.GetDetail() == nil || user.GetDetail().GetBvDetail() == nil {
		return ""
	}
	detail := user.GetDetail().GetBvDetail()
	if detail.GetRtx() != "" {
		return detail.GetRtx()
	}
	if detail.GetId() != "" {
		return detail.GetId()
	}
	return ""
}

func userValid(user *gate.User) bool {
	return user != nil &&
		user.GetId() != "" &&
		user.GetDetail() != nil &&
		user.GetDetail().GetBvDetail() != nil
}

func toSearchConds(conds []*pb.SearchCond) []model.SearchCond {
	condList := make([]model.SearchCond, 0, len(conds))
	for _, cond := range conds {
		condList = append(condList, toSearchCond(cond))
	}
	return condList
}

func toSearchCond(cond *pb.SearchCond) model.SearchCond {
	return model.SearchCond{
		Field:  cond.Field,
		Op:     cond.Op,
		Values: cond.Values,
	}
}

func toPbAsset(asset model.Asset) *pb.Asset {
	return &pb.Asset{
		Id:              asset.ID,
		Tags:            asset.Tags,
		AssetType:       asset.AssetType,
		Data:            asset.Data,
		ProjectIdList:   asset.ProjectIdList,
		State:           int32(asset.State),
		BelvethFid:      asset.BelvethFid,
		Name:            asset.Name,
		ThumbnailFid:    asset.ThumbnailFid,
		CreatedAt:       asset.CreatedAt,
		CreatedBy:       asset.CreatedBy,
		BelvethFilename: asset.BelvethFilename,
	}
}

func toPbAssets(assets []model.Asset) []*pb.Asset {
	pbAssets := make([]*pb.Asset, 0, len(assets))
	for _, asset := range assets {
		pbAssets = append(pbAssets, toPbAsset(asset))
	}
	return pbAssets
}

func toSearchCondGroups(condGroups []*pb.SearchCondGroup) []model.SearchCondGroup {
	condGroupList := make([]model.SearchCondGroup, 0, len(condGroups))
	for _, condGroup := range condGroups {
		condGroupList = append(condGroupList, model.SearchCondGroup{
			Conds: toSearchConds(condGroup.GetConds()),
		})
	}
	return condGroupList
}

func toSortInfos(sortInfos []*pb.SortInfo) []model.SortInfo {
	sortInfoList := make([]model.SortInfo, 0, len(sortInfos))
	for _, sortInfo := range sortInfos {
		sortInfoList = append(sortInfoList, model.SortInfo{
			Field: sortInfo.Field,
			Order: sortInfo.Type,
		})
	}
	return sortInfoList
}
