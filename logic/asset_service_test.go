package logic

import (
	"testing"

	"git.woa.com/BSC/previz_backend/cms_admin/model"
	"git.woa.com/BSC/previz_backend/cms_admin/util"
)

func TestAppendValidConds(t *testing.T) {
	// 准备测试数据：模拟你提供的入参
	searchReq := &model.SearchAssetsReq{
		Page:     1,
		PageSize: 10,
		SearchCondGroups: []model.SearchCondGroup{
			{
				Conds: []model.SearchCond{
					{
						Field:  "primary_key",
						Op:     "EQ",
						Values: "g58gxnq37a",
					},
				},
			},
		},
		Sort: []model.SortInfo{},
	}

	// 打印修改前的状态
	t.Logf("修改前 searchReq: %v", util.Marshal(searchReq))
	t.Logf("修改前 SearchCondGroups 长度: %d", len(searchReq.SearchCondGroups))
	t.Logf("修改前第一个组的条件数量: %d", len(searchReq.SearchCondGroups[0].Conds))

	// 调用被测试的函数
	appendStateValidConds(searchReq)

	// 打印修改后的状态
	t.Logf("修改后 searchReq: %v", util.Marshal(searchReq))
	t.Logf("修改后 SearchCondGroups 长度: %d", len(searchReq.SearchCondGroups))
	t.Logf("修改后第一个组的条件数量: %d", len(searchReq.SearchCondGroups[0].Conds))

	// 验证修改是否生效
	if len(searchReq.SearchCondGroups) != 1 {
		t.Errorf("期望 SearchCondGroups 长度为 1，实际为 %d", len(searchReq.SearchCondGroups))
	}

	firstGroup := searchReq.SearchCondGroups[0]
	if len(firstGroup.Conds) != 2 {
		t.Errorf("期望第一个组的条件数量为 2，实际为 %d", len(firstGroup.Conds))
	}

	// 验证原始条件是否保留
	originalCond := firstGroup.Conds[0]
	if originalCond.Field != "primary_key" || originalCond.Op != "EQ" || originalCond.Values != "g58gxnq37a" {
		t.Errorf("原始条件被修改了: %+v", originalCond)
	}

	// 验证新增的条件是否正确
	addedCond := firstGroup.Conds[1]
	expectedStateValue := "1" // model.AssetStateValid 的值
	if addedCond.Field != "state" || addedCond.Op != "EQ" || addedCond.Values != expectedStateValue {
		t.Errorf("新增的条件不正确: %+v，期望 Field=state, Op=EQ, Values=%s", addedCond, expectedStateValue)
	}

	t.Log("测试通过：appendValidConds 函数成功修改了 searchReq，且修改在函数外可见")
}
