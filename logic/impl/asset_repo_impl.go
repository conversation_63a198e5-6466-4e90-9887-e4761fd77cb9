package impl

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/video_media/universal_mgr"
	"git.code.oa.com/video_media/media_go_commlib/mediapkg/thirdparty/universal"
	universal_pkg "git.code.oa.com/video_media/media_go_commlib/mediapkg/universal"
	"git.woa.com/BSC/previz_backend/cms_admin/model"
	gate "git.woa.com/trpcprotocol/video_media/belveth_gateway_belveth"
)

const (
	projID = 10042
	dimID  = 1
)

// assetRepoImpl 资产仓库实现
type assetRepoImpl struct {
	operator universal.Universal
}

// NewAssetRepoImpl 创建资产仓库实现
func NewAssetRepoImpl(universalRepo universal.Universal) *assetRepoImpl {
	return &assetRepoImpl{
		operator: universalRepo,
	}
}

// GetAsset 获取资产信息
func (c *assetRepoImpl) GetAsset(ctx context.Context, id string) (model.Asset, error) {
	rsp, err := c.operator.Read(ctx, &universal.ReadReq{
		ProjID:      projID,
		DimID:       dimID,
		MediaIDList: []string{id},
	})
	if err != nil {
		return model.Asset{}, fmt.Errorf("获取资产信息失败, err: %v", err)
	}
	mediaInfo, ok := rsp.MediaInfos[id]
	if !ok {
		return model.Asset{}, errors.New("asset not found")
	}
	asset := model.Asset{}
	if err := universal_pkg.ParseMediaInfo(mediaInfo, &asset); err != nil {
		return model.Asset{}, fmt.Errorf("解析资产信息失败, err: %v", err)
	}
	return asset, nil
}

// CreateAsset 创建资产
func (c *assetRepoImpl) CreateAsset(ctx context.Context, asset model.Asset, user *gate.User) (string, error) {
	fieldVals := toFieldVals(asset)
	log.Infof("fieldVals: %v", fieldVals)
	rsp, err := c.operator.Create(ctx, &universal.CreateReq{
		ProjID:    projID,
		DimID:     dimID,
		FieldVals: toFieldVals(asset),
	})
	if err != nil {
		return "", err
	}
	return rsp.MediaID, nil
}

// UpdateAsset 更新资产
func (c *assetRepoImpl) UpdateAsset(ctx context.Context, asset model.Asset, user *gate.User) error {
	err := c.operator.Write(ctx, &universal.WriteReq{
		ProjID:    projID,
		DimID:     dimID,
		MediaID:   asset.ID,
		FieldVals: toFieldVals(asset),
		Operator:  user.GetDetail().GetBvDetail().GetRtx(),
		OpType:    universal_mgr.EnumUpdateType_Set,
	})
	if err != nil {
		return fmt.Errorf("update asset failed, err: %v", err)
	}
	return nil
}

// DeleteAsset 删除资产
func (c *assetRepoImpl) DeleteAsset(ctx context.Context, id string, user *gate.User) error {
	err := c.operator.Write(ctx, &universal.WriteReq{
		ProjID:  projID,
		DimID:   dimID,
		MediaID: id,
		FieldVals: map[string][]string{
			"state": {fmt.Sprintf("%d", model.AssetStateDeleted)},
		},
		Operator: user.GetDetail().GetBvDetail().GetRtx(),
		OpType:   universal_mgr.EnumUpdateType_Set,
	})
	return err
}

func (c *assetRepoImpl) SearchAssets(ctx context.Context, req *model.SearchAssetsReq) (
	model.Pagination, []model.Asset, error) {
	res, err := c.operator.Search(ctx, &universal.SearchReq{
		SearchTotalReq: &universal.SearchTotalReq{
			ProjID: projID,
			DimID:  dimID,
			Sort:   toSortInfos(req.Sort),
			Cond:   toSearchConds(req.SearchCondGroups),
		},
		PageNum:  uint(req.Page),
		PageSize: uint(req.PageSize),
	})
	if err != nil {
		return model.Pagination{}, nil, err
	}

	assetList := make([]model.Asset, 0, len(res.MediaInfos))
	for _, mediaInfo := range res.MediaInfos {
		asset := model.Asset{}
		if err := universal_pkg.ParseMediaInfo(mediaInfo, &asset); err != nil {
			return model.Pagination{}, nil, fmt.Errorf("解析资产信息失败, err: %v", err)
		}
		assetList = append(assetList, asset)
	}

	return model.Pagination{
		Page:     int32(res.PageNum),
		PageSize: int32(res.PageSize),
		Total:    int32(res.Total),
	}, assetList, nil
}

func toSortInfos(sortInfos []model.SortInfo) []*universal_mgr.SortInfo {
	sortInfoList := make([]*universal_mgr.SortInfo, 0, len(sortInfos))
	for _, sortInfo := range sortInfos {
		sortInfoList = append(sortInfoList, &universal_mgr.SortInfo{
			Field: sortInfo.Field,
			Type:  sortInfo.Order,
		})
	}
	return sortInfoList
}

func toSearchConds(condGroups []model.SearchCondGroup) []string {
	condList := make([]string, 0, len(condGroups))
	for _, conds := range condGroups {
		condStr := make([]string, 0, len(conds.Conds))
		for _, cond := range conds.Conds {
			condStr = append(condStr, cond.ToUniversalSearchCond())
		}
		condList = append(condList, strings.Join(condStr, "&"))
	}
	return condList
}

// toFieldVals 将资产信息转换为字段值，用反射的方法，取vmedia标签的值
func toFieldVals(asset model.Asset) map[string][]string {
	result := make(map[string][]string)

	// 使用反射获取结构体信息
	v := reflect.ValueOf(asset)

	// 检查是否为指针类型，如果是则获取元素值
	if v.Kind() == reflect.Ptr {
		if v.IsNil() {
			return result // 返回空 map 而不是 panic
		}
		v = v.Elem()
	}

	// 检查是否为有效值
	if !v.IsValid() {
		return result
	}

	t := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)

		// 检查字段是否有效
		if !field.IsValid() {
			continue
		}

		// 获取 vmedia 标签
		tag := fieldType.Tag.Get("vmedia")
		// 跳过空标签和primaryKey标签
		if tag == "" || tag == "primary_key" {
			continue
		}

		// 根据字段类型处理不同的值
		switch field.Kind() {
		case reflect.String:
			if field.String() != "" {
				result[tag] = []string{field.String()}
			}
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			if field.Int() != 0 {
				result[tag] = []string{fmt.Sprintf("%d", field.Int())}
			}
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			if field.Uint() != 0 {
				result[tag] = []string{fmt.Sprintf("%d", field.Uint())}
			}
		case reflect.Float32, reflect.Float64:
			if field.Float() != 0 {
				result[tag] = []string{fmt.Sprintf("%f", field.Float())}
			}
		case reflect.Bool:
			result[tag] = []string{fmt.Sprintf("%t", field.Bool())}
		case reflect.Slice:
			// 处理切片类型，如 []string
			if field.Len() > 0 {
				sliceValues := make([]string, 0, field.Len())
				for j := 0; j < field.Len(); j++ {
					// 安全地获取切片元素
					if j < field.Len() {
						element := field.Index(j)
						if element.IsValid() {
							sliceValues = append(sliceValues, fmt.Sprintf("%v", element.Interface()))
						}
					}
				}
				if len(sliceValues) > 0 {
					result[tag] = sliceValues
				}
			}
		}
	}

	return result
}

// AddAssetProject 添加资产和项目关联关系
func (c *assetRepoImpl) AddAssetProject(ctx context.Context, assetID, projectID string, user *gate.User) error {
	if projectID == "" || assetID == "" {
		return fmt.Errorf("projectID or assetID is empty")
	}
	err := c.operator.Write(ctx, &universal.WriteReq{
		ProjID:  projID,
		DimID:   dimID,
		MediaID: assetID,
		FieldVals: map[string][]string{
			"project_id_list": {projectID},
		},
		Operator: user.GetDetail().GetBvDetail().GetRtx(),
		OpType:   universal_mgr.EnumUpdateType_Append,
	})
	if err != nil {
		return fmt.Errorf("update asset failed, err: %v", err)
	}
	return nil
}

// RemoveAssetProject 删除资产和项目关联关系
func (c *assetRepoImpl) RemoveAssetProject(ctx context.Context, assetID, projectID string, user *gate.User) error {
	if projectID == "" || assetID == "" {
		return fmt.Errorf("projectID or assetID is empty")
	}
	err := c.operator.Write(ctx, &universal.WriteReq{
		ProjID:  projID,
		DimID:   dimID,
		MediaID: assetID,
		FieldVals: map[string][]string{
			"project_id_list": {projectID},
		},
		Operator: user.GetDetail().GetBvDetail().GetRtx(),
		OpType:   universal_mgr.EnumUpdateType_Del,
	})
	return err
}
