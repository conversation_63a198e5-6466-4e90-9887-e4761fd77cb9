package impl

import (
	"testing"

	"git.woa.com/BSC/previz_backend/cms_admin/model"
)

func TestToFieldVals(t *testing.T) {
	tests := []struct {
		name     string
		asset    model.Asset
		expected map[string][]string
	}{
		{
			name: "正常情况 - 所有字段都有值",
			asset: model.Asset{
				ID:            "test-id",
				Tags:          []string{"tag1", "tag2"},
				AssetType:     model.Other,
				Data:          "test-data",
				State:         model.AssetStateValid,
				ProjectIdList: []string{"proj1", "proj2"},
			},
			expected: map[string][]string{

				"tags":            {"tag1", "tag2"},
				"asset_type":      {model.Other},
				"data":            {"test-data"},
				"state":           {"1"},
				"project_id_list": {"proj1", "proj2"},
			},
		},
		{
			name: "空字符串字段",
			asset: model.Asset{
				ID:            "",
				Tags:          []string{},
				AssetType:     "",
				Data:          "",
				State:         0,
				ProjectIdList: []string{},
			},
			expected: map[string][]string{},
		},
		{
			name: "部分字段有值",
			asset: model.Asset{
				ID:            "test-id",
				Tags:          []string{},
				AssetType:     model.Character,
				Data:          "",
				State:         model.AssetStateDeleted,
				ProjectIdList: []string{"proj1"},
			},
			expected: map[string][]string{
				"asset_type":      {model.Character},
				"state":           {"2"},
				"project_id_list": {"proj1"},
			},
		},
		{
			name: "零值状态",
			asset: model.Asset{
				ID:            "test-id",
				Tags:          []string{"tag1"},
				AssetType:     model.Audio,
				Data:          "sound-data",
				State:         0,
				ProjectIdList: []string{},
			},
			expected: map[string][]string{
				"tags":       {"tag1"},
				"asset_type": {model.Audio},
				"data":       {"sound-data"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := toFieldVals(tt.asset)

			// 检查结果长度
			if len(result) != len(tt.expected) {
				t.Errorf("toFieldVals() 返回的字段数量 = %v, 期望 %v", len(result), len(tt.expected))
			}

			// 检查每个字段的值
			for key, expectedValues := range tt.expected {
				actualValues, exists := result[key]
				if !exists {
					t.Errorf("toFieldVals() 缺少字段 %v", key)
					continue
				}

				if len(actualValues) != len(expectedValues) {
					t.Errorf("字段 %v 的值数量 = %v, 期望 %v", key, len(actualValues), len(expectedValues))
					continue
				}

				for i, expectedValue := range expectedValues {
					if i >= len(actualValues) {
						t.Errorf("字段 %v 的值索引 %v 超出范围", key, i)
						continue
					}
					if actualValues[i] != expectedValue {
						t.Errorf("字段 %v 的值[%v] = %v, 期望 %v", key, i, actualValues[i], expectedValue)
					}
				}
			}
		})
	}
}
