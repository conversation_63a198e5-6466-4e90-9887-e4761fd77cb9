package impl

import (
	"context"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/BSC/previz_backend/cms_admin/consts"
	"git.woa.com/BSC/previz_backend/cms_admin/model"
	"git.woa.com/BSC/previz_backend/cms_admin/util"
	fm "git.woa.com/trpcprotocol/video_media/open_medium_file_inner_v4"
)

type assetUploaderImpl struct {
	jobManagerCli fm.JobManagerClientProxy
}

// NewAssetUploader 创建文件上传器
func NewAssetUploader(jobManagerCli fm.JobManagerClientProxy) model.FileUploader {
	return &assetUploaderImpl{
		jobManagerCli: jobManagerCli,
	}
}

// Start 开始上传
func (a *assetUploaderImpl) Start(ctx context.Context,
	assetID, fileName, contentType, operator string) (string, error) {
	log.Infof("start upload, assetID: %s, operator: %s", assetID, operator)
	var (
		body = &fm.Upload{
			Name:        fileName,
			Storage:     fm.Storage_TENCENT_COS,
			Typ:         fm.FileType_FILE,
			ContentType: contentType,
		}
		params = map[string]*fm.JobParam{}
	)
	params[fm.JobType_JOB_UPLOAD.String()] = &fm.JobParam{Body: &fm.JobParam_Upload{Upload: body}}
	req := &fm.SubmitReq{
		Type:   fm.JobType_JOB_UPLOAD,
		Params: params,
		User:   operator,
	}
	log.Infof("job submit req: %s", util.Marshal(req))
	resp, err := a.jobManagerCli.JobSubmit(ctx, req, client.WithServiceName("trpc.video_media.open_medium_file.InnerV4"))
	if err != nil {
		return "", errs.Newf(consts.CodeInternal, "job submit failed, err: %v", err)
	}
	log.Debugf("job submit resp: %+v", resp)
	if resp.GetId() == "" {
		return "", errs.Newf(consts.CodeInternal, "job submit resp is invalid, resp: %+v", resp)
	}
	return resp.GetId(), nil
}

// Complete 完成上传
func (a *assetUploaderImpl) Complete(ctx context.Context, jobID string, total int) (string, error) {
	var (
		body   = &fm.JobParam_Str{Str: strconv.Itoa(total)}
		params = map[string]*fm.JobParam{}
	)
	params[fm.JobType_JOB_UPLOAD.String()] = &fm.JobParam{Body: body}
	req := &fm.InterveneReq{
		Token:  jobID,
		Type:   fm.JobType_JOB_UPLOAD,
		Status: fm.JobStatus_SUCCESS,
		Params: params,
	}
	log.Infof("job complete req: %s", util.Marshal(req))
	resp, err := a.jobManagerCli.JobIntervene(ctx, req)
	if err != nil {
		return "", errs.Newf(consts.CodeInternal, "job complete failed, err: %v", err)
	}
	data := resp.GetParams()
	ret, ok := data[fm.JobType_JOB_UPLOAD.String()]
	if !ok {
		log.ErrorContext(ctx, resp.String())
		return "", errs.Newf(consts.CodeInternal, "response not right, resp: %+v", resp)
	}
	if ret.GetComplete() == nil || ret.GetComplete().GetFid() == "" {
		return "", errs.Newf(consts.CodeInternal, "response not right, resp: %+v", resp)
	}
	if ret.GetComplete().GetRetry() {
		return "", errs.Newf(consts.CodeInternal, "job complete failed, retry: %v", ret.GetComplete().GetRetry())
	}
	if len(ret.GetComplete().GetFailedList()) > 0 {
		return "", errs.Newf(consts.CodeInternal, "job complete failed, failedList: %v", ret.GetComplete().GetFailedList())
	}
	return ret.GetComplete().GetFid(), nil
}
