package impl

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"git.woa.com/BSC/previz_backend/cms_admin/model"
)

type assetUploadJobRepoImpl struct {
	db *gorm.DB
}

func NewAssetUploadJobRepoImpl(db *gorm.DB) model.AssetUploadJobRepo {
	return &assetUploadJobRepoImpl{
		db: db,
	}
}

func (a *assetUploadJobRepoImpl) Create(ctx context.Context, assetID, jobID, operator, fileName string) (int64, error) {
	job := &model.AssetUploadJob{
		AssetID:   assetID,
		JobID:     jobID,
		FileName:  fileName,
		CreatedBy: operator,
		UpdatedBy: operator,
		State:     model.AssetUploadJobStateInit,
	}
	return a.db.WithContext(ctx).Create(job).RowsAffected, nil
}

func (a *assetUploadJobRepoImpl) GetOngoingJob(ctx context.Context, assetID string) (*model.AssetUploadJob, error) {
	job := &model.AssetUploadJob{}
	if err := a.db.WithContext(ctx).Where("asset_id = ? AND state = ?", assetID, model.AssetUploadJobStateInit).First(job).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return job, nil
}

func (a *assetUploadJobRepoImpl) Save(ctx context.Context, job *model.AssetUploadJob) error {
	return a.db.WithContext(ctx).Save(job).Error
}
