package logic

import (
	"fmt"
	"time"

	tgorm "git.code.oa.com/trpc-go/trpc-database/gorm"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/video_media/universal_mgr"
	"git.code.oa.com/video_media/media_go_commlib/mediapkg/thirdparty/universal"
	"git.woa.com/BSC/previz_backend/cms_admin/conf"
	"git.woa.com/BSC/previz_backend/cms_admin/logic/impl"
	"git.woa.com/BSC/previz_backend/cms_admin/model"
	fm "git.woa.com/trpcprotocol/video_media/open_medium_file_inner_v4"
)

type provider struct {
	universalCli  universal.Universal
	jobManagerCli fm.JobManagerClientProxy

	assetUploader      model.FileUploader
	assetRepo          model.AssetRepo
	assetUploadJobRepo model.AssetUploadJobRepo
}

var (
	providerInstance *provider
)

// InitProvider 初始化 provider
func InitProvider() error {
	if providerInstance != nil {
		log.Warnf("provider 已经初始化过，跳过")
		return nil
	}
	cfg := conf.GetConfig()
	proxy := universal_mgr.NewUniversalMgrClientProxy(
		client.WithNamespace(cfg.Universal.Namespace),
		client.WithTimeout(time.Duration(cfg.Universal.Timeout)*time.Millisecond),
		client.WithDisableServiceRouter(),
	)
	if cfg.Universal.ReadAuth.AppID == "" || cfg.Universal.WriteAuth.AppID == "" || cfg.Universal.SearchAuth.AppID == "" || cfg.Universal.CreateAuth.AppID == "" {
		return fmt.Errorf("universal auth config is not set")
	}
	universalCli := universal.NewUniversalOperator(proxy,
		universal.WithReadAuthInfo(cfg.Universal.ReadAuth.AppID, cfg.Universal.ReadAuth.AppKey),
		universal.WithWriteAuthInfo(cfg.Universal.WriteAuth.AppID, cfg.Universal.WriteAuth.AppKey),
		universal.WithCreateAuthInfo(cfg.Universal.CreateAuth.AppID, cfg.Universal.CreateAuth.AppKey),
		universal.WithSearchAuthInfo(cfg.Universal.SearchAuth.AppID, cfg.Universal.SearchAuth.AppKey),
		universal.WithSearchPageSize(cfg.Universal.SearchBatchSize),
		universal.WithReadBatchSize(cfg.Universal.ReadBatchSize),
		universal.WithRetryTimes(1),
	)

	jobManagerCli := fm.NewJobManagerClientProxy(
		client.WithNamespace(cfg.JobManager.Namespace),
	)
	// init db
	db, err := tgorm.NewClientProxy("trpc.cms_admin.asset.database")
	if err != nil {
		return fmt.Errorf("failed to connect to database: %v", err)
	}

	providerInstance = &provider{
		assetRepo:          impl.NewAssetRepoImpl(universalCli),
		universalCli:       universalCli,
		jobManagerCli:      jobManagerCli,
		assetUploader:      impl.NewAssetUploader(jobManagerCli),
		assetUploadJobRepo: impl.NewAssetUploadJobRepoImpl(db), // TODO: 需要替换为实际的 db
	}
	log.Infof("初始化 provider 成功，assetRepo: %v", providerInstance.assetRepo)
	return nil
}

func getProvider() *provider {
	return providerInstance
}
