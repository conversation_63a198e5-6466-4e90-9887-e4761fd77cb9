package main

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"

	_ "git.code.oa.com/trpc-go/trpc-config-rainbow" // trpc-config-rainbow 和七彩石打通
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	_ "git.code.oa.com/trpc-go/trpc-selector-dsn"
	"git.woa.com/BSC/previz_backend/cms_admin/conf"
	"git.woa.com/BSC/previz_backend/cms_admin/logic"
	_ "git.woa.com/belveth/lib/trpc/plugin"
	_ "git.woa.com/belveth/lib/trpc/plugin/auth"
	_ "git.woa.com/belveth/lib/trpc/sdk/user"
	_ "git.woa.com/galileo/trpc-go-galileo"
	pb "git.woa.com/trpcprotocol/video_media/cms_admin_admin"
)

func main() {
	ctx := context.Background()
	s := trpc.NewServer()
	if err := conf.InitConfig(ctx); err != nil {
		log.Fatalf("init config failed, err: %v", err)
	}
	if err := logic.InitProvider(); err != nil {
		log.Fatalf("init provider failed, err: %v", err)
	}
	svc := logic.NewAssetService()
	pb.RegisterAssetServiceService(
		s.Service("trpc.video_media.cms_admin.AssetService"), svc)
	pb.RegisterInnerAssetServiceService(
		s.Service("trpc.video_media.cms_admin.InnerAssetService"), svc)
	log.Infof("Server starting...")
	s.Serve()
}
